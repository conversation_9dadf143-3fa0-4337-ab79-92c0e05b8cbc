#ifndef PROTOCOL_SERVICE_HPP
#define PROTOCOL_SERVICE_HPP

#include <memory>
#include <queue>
#include <vector>
#include <mutex>
#include <thread>
#include <atomic>
#include <functional>
#include <condition_variable>

#include "zexuan/base/observer.hpp"
#include "zexuan/base/subject.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/message.hpp"

namespace zexuan {
namespace protocol {

// 前向声明
namespace transform {
    class ProtocolTransform;
}

namespace service {

// 协议服务固定ID定义（每个连接独立的Mediator）
constexpr base::ObjectId SERVICE_OBSERVER_ID = base::PROTOCOL_SERVICE_BASE + base::OBSERVER_OFFSET;  // 2000
constexpr base::ObjectId SERVICE_SUBJECT_ID  = base::PROTOCOL_SERVICE_BASE + base::SUBJECT_OFFSET;   // 2050

/**
 * @brief 协议服务类
 * 参考原始的 TNXEcMsgOperationObj 设计
 * 负责业务逻辑处理和消息转换
 */
class ProtocolService {
public:
    // 构造函数 - 使用固定ID，每个连接独立的Mediator
    explicit ProtocolService(std::shared_ptr<base::Mediator> mediator);
    virtual ~ProtocolService();

    // 生命周期管理
    bool Initialize();
    bool Start();
    bool Stop();
    bool IsRunning() const { return is_running_; }

    // 设置Transform（用于协议转换）
    void SetProtocolTransform(std::unique_ptr<transform::ProtocolTransform> transform);

private:
    // 基础成员
    std::shared_ptr<base::Mediator> mediator_;

    // Observer和Subject（参考原始设计）
    std::shared_ptr<base::Observer> observer_;
    std::shared_ptr<base::Subject> subject_;

    // 协议转换器
    std::unique_ptr<transform::ProtocolTransform> protocol_transform_;

    // 状态管理
    std::atomic<bool> is_initialized_{false};
    std::atomic<bool> is_running_{false};
    std::atomic<bool> should_stop_{false};

    // 线程管理
    std::thread business_thread_;

    // 消息队列
    std::queue<base::CommonMessage> common_queue_;
    std::mutex common_queue_mutex_;
    std::condition_variable common_queue_cv_;

    // 消息处理回调
    void OnCommonMessage(const base::CommonMessage& message);
    void OnEventMessage(const base::EventMessage& message);

    // 线程处理函数
    void BusinessMessageProcessorLoop();

    // 业务逻辑处理
    void ProcessBusinessMessage(const base::CommonMessage& message);

    // 队列管理
    bool PushToCommonQueue(const base::CommonMessage& message);
    bool PopFromCommonQueue(base::CommonMessage& message);

    // 工具方法
    std::string GenerateInvokeId();
    uint32_t next_message_id_{1};
};

} // namespace service
} // namespace protocol
} // namespace zexuan

#endif // PROTOCOL_SERVICE_HPP