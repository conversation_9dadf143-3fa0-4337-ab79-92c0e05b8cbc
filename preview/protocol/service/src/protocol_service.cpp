#include "protocol_service.hpp"
#include "protocol_transform.hpp"
#include <spdlog/spdlog.h>

namespace zexuan {
namespace protocol {
namespace service {

ProtocolService::ProtocolService(std::shared_ptr<base::Mediator> mediator)
    : mediator_(mediator) {

    // 创建Observer - 使用固定ID接收来自Gateway的命令
    observer_ = std::make_shared<base::Observer>(SERVICE_OBSERVER_ID, mediator_);

    // 设置Observer的结果回调 - 处理来自Gateway的CommonMessage
    observer_->SetResultCallback([this](const base::CommonMessage& message) -> base::Result<void> {
        OnCommonMessage(message);
        return base::Result<void>{};
    });

    // 设置Observer的事件回调 - 处理来自Gateway的EventMessage
    observer_->SetEventCallback([this](const base::EventMessage& message) -> base::Result<void> {
        OnEventMessage(message);
        return base::Result<void>{};
    });

    // 创建Subject - 使用固定ID向Gateway发送响应和事件
    subject_ = std::make_shared<base::Subject>(SERVICE_SUBJECT_ID, mediator_);

    // 设置Subject的命令回调 - 处理来自Gateway的命令
    subject_->SetCmdCallback([this](const base::CommonMessage& message) -> base::Result<void> {
        OnCommonMessage(message);
        return base::Result<void>{};
    });

    spdlog::info("ProtocolService created with Observer ID: {}, Subject ID: {}",
                 SERVICE_OBSERVER_ID, SERVICE_SUBJECT_ID);
}

ProtocolService::~ProtocolService() {
    Stop();
    spdlog::info("ProtocolService destroyed");
}

bool ProtocolService::Initialize() {
    if (is_initialized_) {
        return true;
    }

    if (!mediator_) {
        spdlog::error("Mediator is null");
        return false;
    }

    // 初始化Observer（包含注册到Mediator）
    auto observer_result = observer_->Init();
    if (!observer_result) {
        spdlog::error("Failed to initialize observer: {}", static_cast<int>(observer_result.error()));
        return false;
    }

    // 初始化Subject（包含注册到Mediator）
    auto subject_result = subject_->Init();
    if (!subject_result) {
        spdlog::error("Failed to initialize subject: {}", static_cast<int>(subject_result.error()));
        return false;
    }

    is_initialized_ = true;
    spdlog::info("ProtocolService initialized");
    return true;
}

bool ProtocolService::Start() {
    if (!is_initialized_) {
        spdlog::error("Service not initialized");
        return false;
    }

    if (is_running_) {
        return true;
    }

    should_stop_ = false;

    // 启动业务处理线程
    business_thread_ = std::thread(&ProtocolService::BusinessMessageProcessorLoop, this);

    is_running_ = true;
    spdlog::info("ProtocolService started");
    return true;
}

bool ProtocolService::Stop() {
    if (!is_running_) {
        return true;
    }

    should_stop_ = true;

    // 通知线程退出
    common_queue_cv_.notify_all();

    // 等待线程结束
    if (business_thread_.joinable()) {
        business_thread_.join();
    }

    is_running_ = false;
    spdlog::info("ProtocolService {} stopped", service_id_);
    return true;
}

void ProtocolService::SetProtocolTransform(std::unique_ptr<transform::ProtocolTransform> transform) {
    protocol_transform_ = std::move(transform);
    spdlog::info("ProtocolService {} set protocol transform", service_id_);
}

// 消息处理回调
void ProtocolService::OnCommonMessage(const base::CommonMessage& message) {
    spdlog::debug("Service {} received CommonMessage from {}", service_id_, message.source_id);

    // 将消息加入队列
    PushToCommonQueue(message);
}

void ProtocolService::OnEventMessage(const base::EventMessage& message) {
    spdlog::debug("Service {} received EventMessage", service_id_);

    // 暂时空实现
    // TODO: 处理事件消息
}

// 线程处理函数
void ProtocolService::BusinessMessageProcessorLoop() {
    spdlog::info("Service {} business processor started", service_id_);

    while (!should_stop_) {
        base::CommonMessage message;

        // 从队列中取消息
        if (PopFromCommonQueue(message)) {
            // 处理业务逻辑
            ProcessBusinessMessage(message);
        }
    }

    spdlog::info("Service {} business processor stopped", service_id_);
}

// 业务消息处理（核心方法）
void ProtocolService::ProcessBusinessMessage(const base::CommonMessage& message) {
    spdlog::debug("Processing business message: invoke_id={}", message.invoke_id);

    // 尝试解析输入的IEC103消息
    base::Message input_msg;
    size_t parsed = input_msg.deserialize(message.data);

    base::CommonMessage response;
    response.type = base::MessageType::RESULT;
    response.source_id = service_id_;
    response.target_id = message.source_id;  // 回给原始发送者
    response.invoke_id = message.invoke_id;

    if (parsed > 0) {
        // 成功解析为IEC103消息，生成对应的响应
        spdlog::debug("Parsed input IEC103 message: TYP={:02X}, COT={:02X}, FUN={:02X}, INF={:02X}",
                     input_msg.getTyp(), input_msg.getVsq(), input_msg.getCot(), input_msg.getFun(), input_msg.getInf());

        // 创建响应消息（交换源和目标）
        base::Message response_msg;
        response_msg.setTyp(input_msg.getTyp());
        response_msg.setVsq(input_msg.getVsq());
        response_msg.setCot(0x07);  // 激活确认
        response_msg.setSource(input_msg.getTarget());  // 交换源和目标
        response_msg.setTarget(input_msg.getSource());
        response_msg.setFun(input_msg.getFun());
        response_msg.setInf(input_msg.getInf());

        // 设置响应内容
        std::string response_content = "RESPONSE_TO_" + input_msg.getTextContent();
        response_msg.setTextContent(response_content);

        // 序列化响应消息
        std::vector<uint8_t> serialized_response;
        size_t size = response_msg.serialize(serialized_response);

        if (size > 0) {
            response.data = serialized_response;
            spdlog::debug("Generated IEC103 response message ({} bytes)", size);
        } else {
            // 序列化失败，使用简单响应
            std::string simple_response = "SIMPLE_RESPONSE_FOR_" + message.invoke_id;
            response.data.assign(simple_response.begin(), simple_response.end());
        }
    } else {
        // 不能解析为IEC103，生成简单响应
        std::string simple_response = "SIMPLE_RESPONSE_FOR_" + message.invoke_id;
        response.data.assign(simple_response.begin(), simple_response.end());
        spdlog::debug("Generated simple response for non-IEC103 message");
    }

    // 通过Subject发送响应给Gateway
    if (subject_) {
        auto result = subject_->SendResult(response);
        if (result) {
            spdlog::debug("Sent response to Gateway: invoke_id={}", response.invoke_id);
        } else {
            spdlog::error("Failed to send response to Gateway: invoke_id={}", response.invoke_id);
        }
    }
}

// 队列管理
bool ProtocolService::PushToCommonQueue(const base::CommonMessage& message) {
    std::lock_guard<std::mutex> lock(common_queue_mutex_);
    common_queue_.push(message);
    common_queue_cv_.notify_one();
    return true;
}

bool ProtocolService::PopFromCommonQueue(base::CommonMessage& message) {
    std::unique_lock<std::mutex> lock(common_queue_mutex_);

    // 等待消息或停止信号
    common_queue_cv_.wait(lock, [this] {
        return !common_queue_.empty() || should_stop_;
    });

    if (should_stop_ && common_queue_.empty()) {
        return false;
    }

    if (!common_queue_.empty()) {
        message = common_queue_.front();
        common_queue_.pop();
        return true;
    }

    return false;
}

} // namespace service
} // namespace protocol
} // namespace zexuan