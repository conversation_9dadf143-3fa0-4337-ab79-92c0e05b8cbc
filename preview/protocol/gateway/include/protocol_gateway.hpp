#ifndef PROTOCOL_GATEWAY_SIMPLE_HPP
#define PROTOCOL_GATEWAY_SIMPLE_HPP

#include <memory>
#include <queue>
#include <map>
#include <vector>
#include <mutex>
#include <thread>
#include <atomic>
#include <chrono>
#include <functional>
#include <condition_variable>
#include <nlohmann/json.hpp>

#include "zexuan/base/observer.hpp"
#include "zexuan/base/subject.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/message.hpp"

namespace zexuan {
namespace protocol {

// 前向声明
namespace transform {
    class ProtocolTransform;
    struct ProtocolFrame;
}
namespace service {
    class ProtocolService;
}

namespace gateway {

// 协议网关固定ID定义（每个连接独立的Mediator）
constexpr base::ObjectId GATEWAY_OBSERVER_ID = base::PROTOCOL_GATEWAY_BASE + base::OBSERVER_OFFSET;  // 1000
constexpr base::ObjectId GATEWAY_SUBJECT_ID  = base::PROTOCOL_GATEWAY_BASE + base::SUBJECT_OFFSET;   // 1050

// 请求管理结构体（支持多帧合并）
struct PendingRequest {
    base::CommonMessage original_command;
    std::chrono::system_clock::time_point send_time;

    // 多帧处理（参考原始 ASK_RES_MATCH_PACKAGE）
    std::vector<transform::ProtocolFrame> protocol_frames;     // 协议帧列表
    std::vector<base::CommonMessage> response_list; // 响应消息列表
    bool received_last_frame = false;               // 是否收到最后帧
    bool is_complete = false;                       // 是否完成

    // 多帧合并辅助字段
    uint16_t expected_asdu_addr = 0;                // 期望的ASDU地址
    uint8_t expected_cot = 0;                       // 期望的传送原因
};

/**
 * @brief 协议网关类 - 简化版
 */
class ProtocolGateway {
public:
    // 构造函数 - 使用固定ID，每个连接独立的Mediator
    ProtocolGateway(const std::string& config_file_path, std::shared_ptr<base::Mediator> mediator);
    
    // 析构函数
    virtual ~ProtocolGateway();

    // 生命周期管理
    bool Start();
    void Stop();
    bool IsRunning() const { return is_running_.load(); }

    // 消息处理接口（直接回调机制）
    void OnNetworkProtocolData(const std::vector<uint8_t>& protocol_data, uint32_t conn_id);
    void OnServiceResponse(const base::CommonMessage& result_msg);
    void OnServiceEvent(const base::EventMessage& event_msg);
    
    // 发送回调设置
    void SetSendCallback(std::function<bool(uint32_t, const std::vector<uint8_t>&)> callback);

    // InvokeId管理
    std::string GenerateInvokeId();

protected:
    // 核心线程方法
    void CommandSenderThreadLoop();
    void ResponseReceiverThreadLoop();
    void RequestMatcherThreadLoop();
    void EventProcessorThreadLoop();

    // 协议转换方法
    std::vector<uint8_t> ConvertResponseToProtocolBytes(const base::CommonMessage& response, const PendingRequest& request);
    std::vector<uint8_t> ConvertEventToProtocolBytes(const base::EventMessage& event);

    // 请求管理方法

    void AddPendingRequest(const std::string& invoke_id, const PendingRequest& request);
    PendingRequest* FindPendingRequest(const std::string& invoke_id);
    void RemovePendingRequest(const std::string& invoke_id);
    
    // 多帧处理方法（参考原始 ProOperation）
    bool AddProtocolFrameToRequest(const std::string& invoke_id, const transform::ProtocolFrame& frame);
    bool AddResponseToRequest(const std::string& invoke_id, const base::CommonMessage& response);
    bool IsRequestComplete(const PendingRequest& request);
    void CheckAndSendCompleteResponse(const std::string& invoke_id, PendingRequest& request);

    // 协议帧解析方法
    bool ParseProtocolFrame(const std::vector<uint8_t>& data, transform::ProtocolFrame& frame);
    bool IsLastFrame(const transform::ProtocolFrame& frame);
    bool FrameBelongsToRequest(const transform::ProtocolFrame& frame, const PendingRequest& request);

    // 组件创建方法
    bool CreateProtocolTransform();
    bool CreateProtocolService();

    // 超时处理
    uint32_t GetTimeoutSeconds(const base::CommonMessage& msg);
    void HandleTimeoutRequest(const std::string& invoke_id);

private:
    // 基础成员
    std::shared_ptr<base::Mediator> mediator_;
    std::string config_file_path_;

    // 配置参数（从JSON读取）
    uint32_t request_timeout_seconds_;
    uint32_t thread_sleep_ms_;
    uint32_t max_pending_requests_;
    bool enable_multi_frame_;
    std::string protocol_type_;

    // 核心组件
    std::unique_ptr<transform::ProtocolTransform> protocol_transform_;
    std::unique_ptr<service::ProtocolService> protocol_service_;

    // Observer和Subject组合
    std::shared_ptr<base::Observer> observer_;
    std::shared_ptr<base::Subject> subject_;
    
    // 发送回调
    std::function<bool(uint32_t, const std::vector<uint8_t>&)> send_callback_;

    // 状态管理
    std::atomic<bool> is_running_{false};
    std::atomic<bool> should_stop_{false};

    // 核心队列
    std::queue<base::CommonMessage> common_queue_;
    std::queue<base::CommonMessage> response_queue_;
    std::queue<base::EventMessage> event_queue_;

    // 队列互斥锁
    std::mutex common_queue_mutex_;
    std::mutex response_queue_mutex_;
    std::mutex event_queue_mutex_;
    std::condition_variable common_queue_cv_;
    std::condition_variable response_queue_cv_;
    std::condition_variable event_queue_cv_;

    // 请求匹配
    std::map<std::string, PendingRequest> pending_requests_;
    std::mutex pending_requests_mutex_;

    // 线程管理
    std::thread command_sender_thread_;
    std::thread response_receiver_thread_;
    std::thread request_matcher_thread_;
    std::thread event_processor_thread_;

    // InvokeId计数器
    std::atomic<uint32_t> invoke_id_counter_{1};
    std::atomic<uint32_t> next_invoke_id_{1};

    // invoke_id 映射（标准格式 -> 原始格式）
    std::map<std::string, std::string> invoke_id_mapping_;
    std::mutex invoke_id_map_mutex_;
};

} // namespace gateway
} // namespace protocol
} // namespace zexuan

#endif // PROTOCOL_GATEWAY_SIMPLE_HPP
