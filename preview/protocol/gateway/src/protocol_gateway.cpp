#include "protocol_gateway.hpp"
#include "protocol_transform.hpp"
#include "protocol_service.hpp"
#include "zexuan/utils/invoke_id_utils.hpp"
#include <spdlog/spdlog.h>
#include <fstream>

namespace zexuan {
namespace protocol {
namespace gateway {

ProtocolGateway::ProtocolGateway(const std::string& config_file_path, std::shared_ptr<base::Mediator> mediator)
    : mediator_(mediator), config_file_path_(config_file_path) {

    // 读取配置文件
    std::ifstream config_file(config_file_path_);
    if (!config_file.is_open()) {
        spdlog::warn("Cannot open config file: {}, using default values", config_file_path_);
        // 使用默认值
        request_timeout_seconds_ = 30;
        thread_sleep_ms_ = 10;
        max_pending_requests_ = 1000;
        enable_multi_frame_ = true;
    } else {
        nlohmann::json config;
        config_file >> config;

        // 读取网关配置
        auto gateway_config = config["protocol"]["gateway"];
        request_timeout_seconds_ = gateway_config.value("request_timeout_seconds", 30);
        thread_sleep_ms_ = gateway_config.value("thread_sleep_ms", 10);
        max_pending_requests_ = gateway_config.value("max_pending_requests", 1000);
        enable_multi_frame_ = gateway_config.value("enable_multi_frame", true);
        protocol_type_ = gateway_config.value("protocol_type", "gw104");
    }
    
    // 创建Observer - 使用固定ID接收来自Service层的响应和事件
    observer_ = std::make_shared<base::Observer>(GATEWAY_OBSERVER_ID, mediator_);

    // 设置Observer的结果回调 - 处理来自Service层的响应
    observer_->SetResultCallback([this](const base::CommonMessage& message) -> base::Result<void> {
        OnServiceResponse(message);
        return base::Result<void>{};
    });

    // 设置Observer的事件回调 - 处理来自Service层的事件
    observer_->SetEventCallback([this](const base::EventMessage& message) -> base::Result<void> {
        OnServiceEvent(message);
        return base::Result<void>{};
    });

    // 创建Subject - 使用固定ID向Service层发送命令
    subject_ = std::make_shared<base::Subject>(GATEWAY_SUBJECT_ID, mediator_);

    // 创建协议转换器
    if (!CreateProtocolTransform()) {
        spdlog::error("Failed to create protocol transform");
        throw std::runtime_error("Failed to create protocol transform");
    }

    // 创建协议服务
    if (!CreateProtocolService()) {
        spdlog::error("Failed to create protocol service");
        throw std::runtime_error("Failed to create protocol service");
    }

    spdlog::info("ProtocolGateway created with Observer ID: {}, Subject ID: {}, protocol: {}",
                 GATEWAY_OBSERVER_ID, GATEWAY_SUBJECT_ID, protocol_type_);
}



ProtocolGateway::~ProtocolGateway() {
    Stop();
    spdlog::info("ProtocolGateway destroyed");
}

bool ProtocolGateway::Start() {
    if (is_running_.load()) {
        spdlog::warn("Gateway already running");
        return true;
    }

    try {
        // 初始化Observer（包含注册到Mediator）
        auto observer_result = observer_->Init();
        if (!observer_result) {
            spdlog::error("Failed to initialize Observer: {}", static_cast<int>(observer_result.error()));
            return false;
        }

        // 初始化Subject（包含注册到Mediator）
        auto subject_result = subject_->Init();
        if (!subject_result) {
            spdlog::error("Failed to initialize Subject: {}", static_cast<int>(subject_result.error()));
            return false;
        }

        // 启动线程
        should_stop_.store(false);
        command_sender_thread_ = std::thread(&ProtocolGateway::CommandSenderThreadLoop, this);
        response_receiver_thread_ = std::thread(&ProtocolGateway::ResponseReceiverThreadLoop, this);
        request_matcher_thread_ = std::thread(&ProtocolGateway::RequestMatcherThreadLoop, this);
        event_processor_thread_ = std::thread(&ProtocolGateway::EventProcessorThreadLoop, this);

        is_running_.store(true);
        spdlog::info("Gateway {} started successfully", gateway_id_);
        return true;

    } catch (const std::exception& e) {
        spdlog::error("Failed to start Gateway {}: {}", gateway_id_, e.what());
        return false;
    }
}

void ProtocolGateway::Stop() {
    if (!is_running_.load()) {
        return;
    }

    spdlog::info("Stopping Gateway {}", gateway_id_);
    
    // 停止线程
    should_stop_.store(true);
    
    // 通知所有条件变量
    common_queue_cv_.notify_all();
    response_queue_cv_.notify_all();
    event_queue_cv_.notify_all();

    // 等待线程结束
    if (command_sender_thread_.joinable()) command_sender_thread_.join();
    if (response_receiver_thread_.joinable()) response_receiver_thread_.join();
    if (request_matcher_thread_.joinable()) request_matcher_thread_.join();
    if (event_processor_thread_.joinable()) event_processor_thread_.join();

    // 从Mediator注销
    if (mediator_) {
        mediator_->CancelObserver(gateway_id_);
        mediator_->CancelSubject(gateway_id_);
    }

    is_running_.store(false);
    spdlog::info("Gateway {} stopped", gateway_id_);
}

void ProtocolGateway::OnNetworkProtocolData(const std::vector<uint8_t>& protocol_data, uint32_t conn_id) {
    spdlog::debug("Gateway {} received protocol data from connection {}, length: {}",
                 gateway_id_, conn_id, protocol_data.size());

    // 解析协议帧
    transform::ProtocolFrame frame;
    if (!ParseProtocolFrame(protocol_data, frame)) {
        spdlog::error("Failed to parse protocol frame from connection {}", conn_id);
        return;
    }

    // 使用Transform转换协议帧为CommonMessage
    if (!protocol_transform_) {
        spdlog::error("Protocol transform not available");
        return;
    }

    transform::ProtocolFrameList frame_list = {frame};
    std::vector<base::CommonMessage> common_list;
    transform::ProtocolFrameList result_frames;

    int ret = protocol_transform_->ConvertProToCommonMsg(frame_list, common_list, result_frames);
    if (ret != 0) {
        spdlog::error("Failed to convert protocol frame to common message");
        return;
    }

    // 处理转换后的CommonMessage
    for (auto& common_msg : common_list) {
        common_msg.source_id = conn_id;
        common_msg.target_id = gateway_id_ + 1000;  // 发送给Service

        // 清空 invoke_id，让 Observer 自动生成标准格式的 invoke_id
        std::string original_invoke_id = common_msg.invoke_id;
        common_msg.invoke_id.clear();  // 清空，让Observer自动生成

        // 通过Observer发送命令给Service（不是SendResult！）
        if (observer_) {
            base::ObjectId service_subject_id = gateway_id_ + 1000 + 100;  // Service Subject ID = Service ID + 100
            spdlog::debug("Attempting to send command to Service Subject {}, Gateway Observer ID: {}",
                         service_subject_id, gateway_id_);

            auto result = observer_->SendCommand(common_msg, service_subject_id);
            if (result) {
                spdlog::debug("Sent command to Service Subject {}: original_invoke_id='{}', new_invoke_id='{}'",
                             service_subject_id, original_invoke_id, common_msg.invoke_id);
            } else {
                spdlog::error("Failed to send command to Service Subject {}: error_code={}",
                             service_subject_id, static_cast<int>(result.error()));
            }
        } else {
            spdlog::error("Gateway Observer is null!");
        }
    }

    // 处理错误回应帧
    if (!result_frames.empty()) {
        for (const auto& error_frame : result_frames) {
            std::vector<uint8_t> error_data = error_frame.data;
            if (send_callback_) {
                send_callback_(conn_id, error_data);
                spdlog::debug("Sent error response frame to connection {}", conn_id);
            }
        }
    }

    spdlog::debug("Gateway {} processed protocol frame: type={}, cot={}, asdu={}, converted to {} messages",
                 gateway_id_, frame.type, frame.cot, frame.asdu_addr, common_list.size());
}

void ProtocolGateway::OnServiceResponse(const base::CommonMessage& result_msg) {
    spdlog::debug("Gateway {} received response from Service: invoke_id={}", gateway_id_, result_msg.invoke_id);

    // 直接发送Service生成的响应数据（已经是序列化的IEC103消息）
    if (send_callback_ && !result_msg.data.empty()) {
        // 从invoke_id中解析连接ID，或使用target_id
        uint32_t conn_id = static_cast<uint32_t>(result_msg.target_id);

        // 检查数据是否是有效的IEC103消息
        base::Message test_msg;
        size_t parsed = test_msg.deserialize(result_msg.data);

        if (parsed > 0) {
            // 是有效的IEC103消息，直接发送
            send_callback_(conn_id, result_msg.data);
            spdlog::debug("Sent IEC103 response to connection {}, length: {} bytes", conn_id, result_msg.data.size());

            // 显示发送的消息详情
            spdlog::debug("Response message: TYP={:02X}, COT={:02X}, SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
                         test_msg.getTyp(), test_msg.getCot(), test_msg.getSource(), test_msg.getTarget(),
                         test_msg.getFun(), test_msg.getInf());
        } else {
            // 不是IEC103消息，直接发送原始数据
            send_callback_(conn_id, result_msg.data);
            spdlog::debug("Sent raw response to connection {}, length: {} bytes", conn_id, result_msg.data.size());
        }
    } else {
        spdlog::warn("No send callback or empty response data");
    }
}

void ProtocolGateway::OnServiceEvent(const base::EventMessage& event_msg) {
    // 添加到事件队列
    {
        std::lock_guard<std::mutex> lock(event_queue_mutex_);
        event_queue_.push(event_msg);
        event_queue_cv_.notify_one();
    }
}

void ProtocolGateway::SetSendCallback(std::function<bool(uint32_t, const std::vector<uint8_t>&)> callback) {
    send_callback_ = callback;
}

std::string ProtocolGateway::GenerateInvokeId() {
    uint32_t counter = invoke_id_counter_.fetch_add(1);
    std::string sequence = std::to_string(counter);
    return utils::invoke_id::Generate(gateway_id_, sequence);
}

// 线程方法实现
void ProtocolGateway::CommandSenderThreadLoop() {
    spdlog::info("Command sender thread started for Gateway {}", gateway_id_);
    
    while (!should_stop_.load()) {
        base::CommonMessage cmd_msg;
        
        // 等待命令
        {
            std::unique_lock<std::mutex> lock(common_queue_mutex_);
            common_queue_cv_.wait(lock, [this] { return !common_queue_.empty() || should_stop_.load(); });
            
            if (should_stop_.load()) break;
            
            if (!common_queue_.empty()) {
                cmd_msg = common_queue_.front();
                common_queue_.pop();
            } else {
                continue;
            }
        }
        
        try {
            // 创建待处理请求
            PendingRequest request;
            request.original_command = cmd_msg;
            request.send_time = std::chrono::system_clock::now();
            
            // 添加到待处理列表
            AddPendingRequest(cmd_msg.invoke_id, request);
            
            // 发送给Service层
            observer_->SendCommand(cmd_msg, SERVICE_ID);
            
            spdlog::debug("Sent command to service, invoke_id: {}", cmd_msg.invoke_id);
            
        } catch (const std::exception& e) {
            spdlog::error("Exception in command sender: {}", e.what());
        }
    }
    
    spdlog::info("Command sender thread stopped for Gateway {}", gateway_id_);
}

void ProtocolGateway::ResponseReceiverThreadLoop() {
    spdlog::info("Response receiver thread started for Gateway {}", gateway_id_);
    
    while (!should_stop_.load()) {
        base::CommonMessage response_msg;
        
        // 等待响应
        {
            std::unique_lock<std::mutex> lock(response_queue_mutex_);
            response_queue_cv_.wait(lock, [this] { return !response_queue_.empty() || should_stop_.load(); });
            
            if (should_stop_.load()) break;
            
            if (!response_queue_.empty()) {
                response_msg = response_queue_.front();
                response_queue_.pop();
            } else {
                continue;
            }
        }
        
        try {
            std::string invoke_id = response_msg.invoke_id;
            auto request = FindPendingRequest(invoke_id);
            
            if (request) {
                // 添加响应到请求中（支持多帧）
                bool is_complete = AddResponseToRequest(invoke_id, response_msg);
                
                if (is_complete) {
                    // 发送完整响应
                    CheckAndSendCompleteResponse(invoke_id, *request);
                    RemovePendingRequest(invoke_id);
                    spdlog::debug("Sent complete response for invoke_id: {}", invoke_id);
                } else {
                    spdlog::debug("Waiting for more response frames for invoke_id: {}", invoke_id);
                }
            } else {
                spdlog::warn("No matching request found for invoke_id: {}", invoke_id);
            }
            
        } catch (const std::exception& e) {
            spdlog::error("Exception in response receiver: {}", e.what());
        }
    }
    
    spdlog::info("Response receiver thread stopped for Gateway {}", gateway_id_);
}

void ProtocolGateway::RequestMatcherThreadLoop() {
    spdlog::info("Request matcher thread started for Gateway {}", gateway_id_);

    while (!should_stop_.load()) {
        try {
            // 检查超时请求
            std::vector<std::string> timeout_requests;
            {
                std::lock_guard<std::mutex> lock(pending_requests_mutex_);
                auto now = std::chrono::system_clock::now();

                for (auto& [invoke_id, request] : pending_requests_) {
                    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - request.send_time);
                    if (duration.count() > request_timeout_seconds_) {
                        timeout_requests.push_back(invoke_id);
                    }
                }
            }

            // 处理超时请求
            for (const std::string& invoke_id : timeout_requests) {
                HandleTimeoutRequest(invoke_id);
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(thread_sleep_ms_));

        } catch (const std::exception& e) {
            spdlog::error("Exception in request matcher: {}", e.what());
        }
    }

    spdlog::info("Request matcher thread stopped for Gateway {}", gateway_id_);
}

void ProtocolGateway::EventProcessorThreadLoop() {
    spdlog::info("Event processor thread started for Gateway {}", gateway_id_);

    while (!should_stop_.load()) {
        base::EventMessage event_message;

        // 等待事件
        {
            std::unique_lock<std::mutex> lock(event_queue_mutex_);
            event_queue_cv_.wait(lock, [this] { return !event_queue_.empty() || should_stop_.load(); });

            if (should_stop_.load()) break;

            if (!event_queue_.empty()) {
                event_message = event_queue_.front();
                event_queue_.pop();
            } else {
                continue;
            }
        }

        try {
            // 转换事件为协议字节流并发送
            std::vector<uint8_t> protocol_event = ConvertEventToProtocolBytes(event_message);

            if (send_callback_) {
                uint32_t conn_id = static_cast<uint32_t>(event_message.source_id);
                bool result = send_callback_(conn_id, protocol_event);

                if (result) {
                    spdlog::debug("Sent event to network, type: {}", event_message.event_type);
                } else {
                    spdlog::error("Failed to send event to network, type: {}", event_message.event_type);
                }
            }

        } catch (const std::exception& e) {
            spdlog::error("Exception in event processor: {}", e.what());
        }
    }

    spdlog::info("Event processor thread stopped for Gateway {}", gateway_id_);
}

// 请求管理方法
void ProtocolGateway::AddPendingRequest(const std::string& invoke_id, const PendingRequest& request) {
    std::lock_guard<std::mutex> lock(pending_requests_mutex_);
    pending_requests_[invoke_id] = request;
}

PendingRequest* ProtocolGateway::FindPendingRequest(const std::string& invoke_id) {
    std::lock_guard<std::mutex> lock(pending_requests_mutex_);
    auto it = pending_requests_.find(invoke_id);
    return (it != pending_requests_.end()) ? &it->second : nullptr;
}

void ProtocolGateway::RemovePendingRequest(const std::string& invoke_id) {
    std::lock_guard<std::mutex> lock(pending_requests_mutex_);
    pending_requests_.erase(invoke_id);
}

// 多帧处理方法
bool ProtocolGateway::AddProtocolFrameToRequest(const std::string& invoke_id, const transform::ProtocolFrame& frame) {
    std::lock_guard<std::mutex> lock(pending_requests_mutex_);
    auto it = pending_requests_.find(invoke_id);
    if (it == pending_requests_.end()) {
        return false;
    }

    PendingRequest& request = it->second;

    // 检查帧是否属于这个请求
    if (!FrameBelongsToRequest(frame, request)) {
        spdlog::warn("Frame does not belong to request {}", invoke_id);
        return false;
    }

    // 添加帧到列表中
    request.protocol_frames.push_back(frame);

    // 检查是否是最后一帧
    if (IsLastFrame(frame)) {
        request.received_last_frame = true;
    }

    // 检查请求是否完成
    request.is_complete = IsRequestComplete(request);

    return request.is_complete;
}

bool ProtocolGateway::AddResponseToRequest(const std::string& invoke_id, const base::CommonMessage& response) {
    std::lock_guard<std::mutex> lock(pending_requests_mutex_);
    auto it = pending_requests_.find(invoke_id);
    if (it == pending_requests_.end()) {
        return false;
    }

    PendingRequest& request = it->second;
    request.response_list.push_back(response);

    // 检查是否是最后一帧
    if (response.type == base::MessageType::RESULT) {
        request.received_last_frame = true;
    }

    request.is_complete = IsRequestComplete(request);
    return request.is_complete;
}

bool ProtocolGateway::IsRequestComplete(const PendingRequest& request) {
    return request.received_last_frame;
}

void ProtocolGateway::CheckAndSendCompleteResponse(const std::string& invoke_id, PendingRequest& request) {
    if (request.response_list.empty()) {
        return;
    }

    // 发送最后一个响应
    const auto& final_response = request.response_list.back();
    std::vector<uint8_t> protocol_response = ConvertResponseToProtocolBytes(final_response, request);

    if (send_callback_) {
        uint32_t conn_id = static_cast<uint32_t>(request.original_command.source_id);
        send_callback_(conn_id, protocol_response);
    }
}

void ProtocolGateway::HandleTimeoutRequest(const std::string& invoke_id) {
    auto request = FindPendingRequest(invoke_id);
    if (!request) {
        return;
    }

    // 生成超时响应
    std::string timeout_str = "TIMEOUT:invoke_id=" + invoke_id + ";";
    std::vector<uint8_t> timeout_protocol(timeout_str.begin(), timeout_str.end());

    if (send_callback_) {
        uint32_t conn_id = static_cast<uint32_t>(request->original_command.source_id);
        send_callback_(conn_id, timeout_protocol);
    }

    RemovePendingRequest(invoke_id);
    spdlog::warn("Request timeout handled, invoke_id: {}", invoke_id);
}

// 协议转换方法（临时实现）
std::vector<uint8_t> ProtocolGateway::ConvertResponseToProtocolBytes(const base::CommonMessage& response, const PendingRequest& request) {
    std::string protocol_str = "RESPONSE:invoke_id=" + response.invoke_id + ";";
    return std::vector<uint8_t>(protocol_str.begin(), protocol_str.end());
}

std::vector<uint8_t> ProtocolGateway::ConvertEventToProtocolBytes(const base::EventMessage& event) {
    std::string protocol_str = "EVENT:type=" + std::to_string(event.event_type) + ";desc=" + event.description + ";";
    return std::vector<uint8_t>(protocol_str.begin(), protocol_str.end());
}

// 组件创建方法
bool ProtocolGateway::CreateProtocolTransform() {
    transform::ProtocolTransformFactory::ProtocolType type;

    if (protocol_type_ == "gw104") {
        type = transform::ProtocolTransformFactory::ProtocolType::gw104;
    } else {
        spdlog::error("Unsupported protocol type: {}", protocol_type_);
        return false;
    }

    protocol_transform_ = transform::ProtocolTransformFactory::CreateTransform(type);
    if (!protocol_transform_) {
        spdlog::error("Failed to create transform for protocol: {}", protocol_type_);
        return false;
    }

    spdlog::info("Created protocol transform: {}", protocol_type_);
    return true;
}

bool ProtocolGateway::CreateProtocolService() {
    // 创建Service，使用独立的ID
    base::ObjectId service_id = gateway_id_ + 1000;  // Service ID = Gateway ID + 1000

    protocol_service_ = std::make_unique<service::ProtocolService>(mediator_, service_id);
    if (!protocol_service_) {
        spdlog::error("Failed to create protocol service");
        return false;
    }

    // 将Transform注入到Service中
    // 注意：这里需要创建Transform的副本，因为Gateway也需要使用Transform
    auto service_transform = transform::ProtocolTransformFactory::CreateTransform(
        transform::ProtocolTransformFactory::ProtocolType::gw104);
    protocol_service_->SetProtocolTransform(std::move(service_transform));

    // 初始化并启动Service
    if (!protocol_service_->Initialize()) {
        spdlog::error("Failed to initialize protocol service");
        return false;
    }

    if (!protocol_service_->Start()) {
        spdlog::error("Failed to start protocol service");
        return false;
    }

    spdlog::info("Created and started protocol service with ID: {}", service_id);
    return true;
}

// 协议帧解析方法
bool ProtocolGateway::ParseProtocolFrame(const std::vector<uint8_t>& data, transform::ProtocolFrame& frame) {
    if (data.empty()) {
        return false;
    }

    // 尝试解析为标准的 IEC 60870-5-103 消息
    base::Message msg;
    size_t parsed = msg.deserialize(data);

    if (parsed > 0) {
        // 成功解析为IEC103消息
        frame.data = data;
        frame.timestamp = std::chrono::system_clock::now();
        frame.type = msg.getTyp();
        frame.cot = msg.getCot();
        frame.asdu_addr = msg.getTarget();  // 使用目标地址作为ASDU地址
        frame.vsq = msg.getVsq();
        frame.is_last_frame = true;
        frame.frame_id = next_invoke_id_++;

        spdlog::debug("Parsed IEC103 frame: TYP={:02X}, VSQ={:02X}, COT={:02X}, SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}, data_len={}",
                     msg.getTyp(), msg.getVsq(), msg.getCot(), msg.getSource(), msg.getTarget(), msg.getFun(), msg.getInf(), data.size());

        return true;
    } else {
        // 如果不能解析为IEC103，尝试简单解析
        frame.data = data;
        frame.timestamp = std::chrono::system_clock::now();

        if (data.size() >= 3) {
            frame.type = data[0];      // 第一个字节作为类型
            frame.cot = data[1];       // 第二个字节作为传输原因
            frame.asdu_addr = data[2]; // 第三个字节作为ASDU地址
        } else {
            // 默认值
            frame.type = 0x01;    // 标准类型
            frame.cot = 0x06;     // 激活
            frame.asdu_addr = 1;  // ASDU地址1
        }

        frame.vsq = 0x81;  // 单信息
        frame.is_last_frame = true;
        frame.frame_id = next_invoke_id_++;

        spdlog::debug("Parsed simple frame: type={:02X}, cot={:02X}, asdu={}, data_len={}",
                     frame.type, frame.cot, frame.asdu_addr, data.size());

        return true;
    }
}

bool ProtocolGateway::IsLastFrame(const transform::ProtocolFrame& frame) {
    return frame.is_last_frame;
}

bool ProtocolGateway::FrameBelongsToRequest(const transform::ProtocolFrame& frame, const PendingRequest& request) {
    // 简单判断：相同的ASDU地址和传输原因
    return (frame.asdu_addr == request.expected_asdu_addr &&
            frame.cot == request.expected_cot);
}

} // namespace gateway
} // namespace protocol
} // namespace zexuan
