/**
 * @file callback_manager.hpp
 * @brief 通用回调管理器 - 消除回调管理代码重复
 * <AUTHOR> for better code reuse
 * @date 2025-08-17
 */

#ifndef ZEXUAN_BASE_CALLBACK_MANAGER_H
#define ZEXUAN_BASE_CALLBACK_MANAGER_H

#include "base_types.hpp"
#include <functional>
#include <optional>
#include <mutex>
#include <type_traits>

namespace zexuan {
namespace base {

/**
 * @brief 通用回调管理器模板类
 * 
 * 提供线程安全的回调函数管理功能，消除派生类中的重复代码
 * 
 * @tparam CallbackType 回调函数类型
 */
template<typename CallbackType>
class CallbackManager {
public:
    static_assert(std::is_invocable_v<CallbackType>, "CallbackType must be invocable");

    /**
     * @brief 默认构造函数
     */
    CallbackManager() = default;

    /**
     * @brief 析构函数
     */
    ~CallbackManager() = default;

    // 禁用拷贝和移动
    CallbackManager(const CallbackManager&) = delete;
    CallbackManager& operator=(const CallbackManager&) = delete;
    CallbackManager(CallbackManager&&) = delete;
    CallbackManager& operator=(CallbackManager&&) = delete;

    /**
     * @brief 设置回调函数
     * @param callback 回调函数
     * @return 操作结果
     */
    VoidResult SetCallback(CallbackType callback) noexcept {
        try {
            std::lock_guard<std::mutex> lock(mutex_);
            callback_ = std::move(callback);
            return {};
        } catch (...) {
            return std::unexpected(ErrorCode::OPERATION_FAILED);
        }
    }

    /**
     * @brief 获取回调函数的副本
     * @return 回调函数的可选值
     */
    std::optional<CallbackType> GetCallback() const noexcept {
        try {
            std::lock_guard<std::mutex> lock(mutex_);
            return callback_;
        } catch (...) {
            return std::nullopt;
        }
    }

    /**
     * @brief 检查是否设置了回调函数
     * @return true 如果已设置回调函数
     */
    bool HasCallback() const noexcept {
        try {
            std::lock_guard<std::mutex> lock(mutex_);
            return callback_.has_value();
        } catch (...) {
            return false;
        }
    }

    /**
     * @brief 清除回调函数
     * @return 操作结果
     */
    VoidResult ClearCallback() noexcept {
        try {
            std::lock_guard<std::mutex> lock(mutex_);
            callback_.reset();
            return {};
        } catch (...) {
            return std::unexpected(ErrorCode::OPERATION_FAILED);
        }
    }

    /**
     * @brief 安全调用回调函数
     * @tparam Args 参数类型
     * @param args 参数
     * @return 回调函数的返回值，如果未设置回调则返回错误
     */
    template<typename... Args>
    auto SafeInvoke(Args&&... args) const -> std::invoke_result_t<CallbackType, Args...> {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!callback_.has_value()) {
            if constexpr (std::is_same_v<std::invoke_result_t<CallbackType, Args...>, VoidResult>) {
                return std::unexpected(ErrorCode::CALLBACK_NOT_SET);
            } else {
                using ReturnType = std::invoke_result_t<CallbackType, Args...>;
                if constexpr (std::is_same_v<ReturnType, Result<void>>) {
                    return std::unexpected(ErrorCode::CALLBACK_NOT_SET);
                } else {
                    // 对于其他Result<T>类型
                    return std::unexpected(ErrorCode::CALLBACK_NOT_SET);
                }
            }
        }

        try {
            return callback_.value()(std::forward<Args>(args)...);
        } catch (...) {
            if constexpr (std::is_same_v<std::invoke_result_t<CallbackType, Args...>, VoidResult>) {
                return std::unexpected(ErrorCode::OPERATION_FAILED);
            } else {
                return std::unexpected(ErrorCode::OPERATION_FAILED);
            }
        }
    }

private:
    /** @brief 回调函数 */
    std::optional<CallbackType> callback_;
    
    /** @brief 保护回调函数的互斥锁 */
    mutable std::mutex mutex_;
};

// ============================================================================
// 常用回调管理器类型别名
// ============================================================================
using CommonMessageCallbackManager = CallbackManager<CommonMessageHandler>;
using EventMessageCallbackManager = CallbackManager<EventMessageHandler>;

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_CALLBACK_MANAGER_H
