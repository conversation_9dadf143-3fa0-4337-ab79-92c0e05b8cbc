/**
 * @file base_types.h
 * @brief 现代化 Subject-Observer-Register-Mediator 设计模式基础类型定义
 * <AUTHOR> from NX framework
 * @date 2025-08-16
 */

#ifndef ZEXUAN_BASE_TYPES_H
#define ZEXUAN_BASE_TYPES_H

// ============================================================================
// 标准库头文件
// ============================================================================
#include <atomic>
#include <concepts>
#include <expected>
#include <functional>
#include <map>
#include <memory>
#include <mutex>
#include <optional>
#include <ranges>
#include <string>
#include <variant>
#include <vector>

namespace zexuan {
namespace base {

// ============================================================================
// 前向声明
// ============================================================================
class RegisterObject;

// ============================================================================
// 基础类型定义
// ============================================================================
using DeviceId = int;
using ObjectId = int;
using EventType = int;

// ============================================================================
// 常量定义
// ============================================================================
constexpr ObjectId INVALID_ID = -1;
constexpr DeviceId ALL_DEVICES = -1;


// ============================================================================
// 标准ID分配规范
// ============================================================================

/// @brief ID范围分配
constexpr ObjectId ID_RANGE_SYSTEM     = 1000;  ///< 1000-1999: 系统组件
constexpr ObjectId ID_RANGE_PROTOCOL   = 2000;  ///< 2000-2999: 协议层
constexpr ObjectId ID_RANGE_SERVICE    = 3000;  ///< 3000-3999: 服务层
constexpr ObjectId ID_RANGE_APPLICATION = 4000; ///< 4000-4999: 应用层
constexpr ObjectId ID_RANGE_USER       = 5000;  ///< 5000+: 用户自定义

/// @brief 协议层ID细分
constexpr ObjectId PROTOCOL_GATEWAY_BASE    = 2000; ///< 2000-2099: 协议网关
constexpr ObjectId PROTOCOL_SERVICE_BASE    = 2100; ///< 2100-2199: 协议服务
constexpr ObjectId PROTOCOL_TRANSFORM_BASE  = 2200; ///< 2200-2299: 协议转换

/// @brief 每个组件的Observer/Subject ID偏移
constexpr ObjectId OBSERVER_OFFSET = 0;   ///< 基础ID作为Observer ID
constexpr ObjectId SUBJECT_OFFSET  = 50;  ///< 基础ID + 50作为Subject ID

/// @brief 标准ID生成宏
#define MAKE_OBSERVER_ID(base_id) ((base_id) + OBSERVER_OFFSET)
#define MAKE_SUBJECT_ID(base_id)  ((base_id) + SUBJECT_OFFSET)

// ============================================================================
// 枚举类型定义
// ============================================================================

/// @brief 消息类型枚举
enum class MessageType : int {
    COMMAND = 1,    ///< 命令消息
    RESULT = 2,     ///< 结果消息
    EVENT = 3       ///< 事件消息
};

/// @brief 设备类别枚举
enum class DeviceCategory : int {
    UNKNOWN = 0,        ///< 未知设备
    IED = 1,           ///< 智能电子设备
    SWITCH = 2,        ///< 开关设备
    TRANSFORMER = 3    ///< 变压器设备
};


/// @brief 错误码枚举
enum class ErrorCode : int {
    SUCCESS = 0,                ///< 成功
    INVALID_PARAMETER,          ///< 无效参数
    OBJECT_NOT_FOUND,          ///< 对象未找到
    ALREADY_EXISTS,            ///< 对象已存在
    CALLBACK_NOT_SET,          ///< 回调函数未设置
    MEDIATOR_NOT_AVAILABLE,    ///< 中介者不可用
    OPERATION_FAILED           ///< 操作失败
};

// ============================================================================
// 结构体定义
// ============================================================================

/// @brief 设备UUID结构
struct DeviceUUID {
    DeviceId device_id{INVALID_ID};
    DeviceCategory category{DeviceCategory::UNKNOWN};

    /// @brief 默认构造函数
    DeviceUUID() = default;

    /// @brief 带参构造函数
    DeviceUUID(DeviceId id, DeviceCategory cat) noexcept
        : device_id(id), category(cat) {}

    /// @brief 比较运算符（用于 std::map）
    auto operator<=>(const DeviceUUID& other) const noexcept = default;

    /// @brief 相等运算符
    bool operator==(const DeviceUUID& other) const noexcept = default;
};

/// @brief 通用消息结构
struct CommonMessage {
    MessageType type{MessageType::COMMAND};
    ObjectId source_id{INVALID_ID};
    ObjectId target_id{INVALID_ID};
    std::string invoke_id;
    std::vector<uint8_t> data;

    /// @brief 默认构造函数
    CommonMessage() = default;
};

/// @brief 事件消息结构
struct EventMessage {
    EventType event_type{0};
    DeviceUUID device_uuid;
    ObjectId source_id{INVALID_ID};
    std::string description;
    std::vector<uint8_t> data;

    /// @brief 默认构造函数
    EventMessage() = default;
};

// ============================================================================
// 类型别名
// ============================================================================
using EventTypeList = std::vector<EventType>;
using DeviceList = std::vector<DeviceUUID>;

// ============================================================================
// 现代化错误处理类型
// ============================================================================
template<typename T>
using Result = std::expected<T, ErrorCode>;

using VoidResult = std::expected<void, ErrorCode>;

// ============================================================================
// 现代化回调函数类型定义
// ============================================================================
using CommonMessageHandler = std::function<Result<void>(const CommonMessage&)>;
using EventMessageHandler = std::function<Result<void>(const EventMessage&)>;

// ============================================================================
// 接口定义 - 遵循接口隔离原则
// ============================================================================

/// @brief 可注册接口 - 定义对象注册到中介者的能力
class IRegisterable {
public:
    virtual ~IRegisterable() = default;

    /// @brief 将自身注册到服务中介
    virtual VoidResult RegisterToMediator() = 0;

    /// @brief 将自身从服务中介注销
    virtual VoidResult CancelFromMediator() noexcept = 0;

    /// @brief 获取对象ID
    virtual ObjectId GetObjectId() const noexcept = 0;
};

/// @brief 事件感知接口 - 定义对事件的关注能力
class IEventAware {
public:
    virtual ~IEventAware() = default;

    /// @brief 设置关注的事件类型
    virtual VoidResult SetCareEventType(std::optional<EventTypeList> event_types) = 0;

    /// @brief 判断是否关注指定事件类型
    virtual bool IsCareEventInfo(EventType event_type) const noexcept = 0;
};

/// @brief 设备感知接口 - 定义对设备的关注能力
class IDeviceAware {
public:
    virtual ~IDeviceAware() = default;

    /// @brief 设置关注的设备列表
    virtual VoidResult SetCareDevices(std::optional<DeviceList> devices) = 0;

    /// @brief 判断是否关注指定设备
    virtual bool IsCareDevInfo(const DeviceUUID& device_id) const noexcept = 0;
};

/// @brief 中介者服务接口 - 定义中介者的核心服务
class IMediatorService {
public:
    virtual ~IMediatorService() = default;

    /// @brief 注册对象到中介者
    virtual VoidResult RegisterObject(std::shared_ptr<IRegisterable> object) = 0;

    /// @brief 从中介者注销对象
    virtual VoidResult UnregisterObject(ObjectId object_id) noexcept = 0;
};



// ============================================================================
// 注册对象信息结构
// ============================================================================

/// @brief 注册对象信息结构
struct RegisterObjectInfo {
    ObjectId object_id{INVALID_ID};
    std::string description;
    std::optional<EventTypeList> event_types;
    std::optional<DeviceList> devices;

    /// @brief 设置对象指针（使用 concepts 约束）
    template<typename T>
    requires std::derived_from<T, RegisterObject>
    void set_object_ptr(std::shared_ptr<T> ptr) noexcept {
        object_ptr_ = std::static_pointer_cast<RegisterObject>(ptr);
    }

    /// @brief 获取对象指针
    std::shared_ptr<RegisterObject> get_object_ptr() const noexcept {
        return object_ptr_.lock();
    }

    /// @brief 检查是否设置了事件类型
    bool has_events() const noexcept {
        return event_types.has_value();
    }

    /// @brief 检查是否设置了设备列表
    bool has_devices() const noexcept {
        return devices.has_value();
    }

private:
    std::weak_ptr<RegisterObject> object_ptr_;
};

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_TYPES_H
